import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useDocuments } from '@/contexts/DocumentContext';
import { Document, STATUS_COLORS } from '@/models/document';
import {
  MoreHorizontal,
  Edit,
  Download,
  Trash2,
  Paperclip,
  Calendar,
  User,
  Building,
  FileText,
  Shield,
  Eye
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { format } from 'date-fns';
import { viewFile, downloadFile } from '@/utils/fileUtils';

interface DocumentCardProps {
  document: Document;
}

const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {
  const { deleteDocument, setCurrentEditId } = useDocuments();

  const handleEdit = () => {
    setCurrentEditId(document.id);
    // Scroll to form
    const formElement = window.document.querySelector('[data-document-form]');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleDelete = () => {
    deleteDocument(document.id);
  };

  const handleView = () => {
    if (document.fileData && document.fileName) {
      viewFile(document.fileData, document.fileName);
    }
  };

  const handleDownload = () => {
    if (document.fileData && document.fileName) {
      downloadFile(document.fileData, document.fileName);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch {
      return dateString;
    }
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'Public':
        return 'bg-green-100 text-green-800';
      case 'Internal':
        return 'bg-yellow-100 text-yellow-800';
      case 'Confidential':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <code className="text-sm bg-muted px-2 py-1 rounded font-mono">
                {document.documentReferenceNumber}
              </code>
              <Badge variant="outline">v{document.version}</Badge>
            </div>
            <h3 className="font-semibold text-base leading-tight mb-2 line-clamp-2">
              {document.documentTitle}
            </h3>
            <div className="flex items-center gap-2 mb-2">
              <Badge
                variant="secondary"
                className={STATUS_COLORS[document.status]}
              >
                {document.status}
              </Badge>
              <Badge
                variant="outline"
                className={getAccessLevelColor(document.accessLevel)}
              >
                <Shield className="h-3 w-3 mr-1" />
                {document.accessLevel}
              </Badge>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              {document.fileName && (
                <>
                  <DropdownMenuItem onClick={handleView}>
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDownload}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </DropdownMenuItem>
                </>
              )}
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem
                    onSelect={(e) => e.preventDefault()}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Document</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete "{document.documentTitle}"?
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Key Information */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Issue:</span>
              <span>{formatDate(document.issueDate)}</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Author:</span>
              <span className="truncate">{document.authorFunction}</span>
            </div>
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Dept:</span>
              <span className="truncate">{document.responsibleDepartment}</span>
            </div>
            {document.approvalDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Validated:</span>
                <span>{formatDate(document.approvalDate)}</span>
              </div>
            )}
          </div>

          {/* File Attachment */}
          {document.fileName && (
            <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
              <Paperclip className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm truncate flex-1">{document.fileName}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleView}
                className="h-6 w-6 p-0"
                title={`View ${document.fileName}`}
              >
                <Eye className="h-3 w-3" />
              </Button>
            </div>
          )}

          {/* Storage Location */}
          <div className="flex items-center gap-2 text-sm">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Storage:</span>
            <span className="truncate">{document.storageLocation}</span>
          </div>

          {/* Additional Details (if available) */}
          {(document.revisionDate || document.distributionMethod || document.remarks) && (
            <div className="pt-2 border-t space-y-1">
              {document.revisionDate && (
                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">Last Revision:</span> {formatDate(document.revisionDate)}
                  {document.revisionReason && (
                    <span className="block mt-1">Reason: {document.revisionReason}</span>
                  )}
                </div>
              )}
              {document.distributionMethod && (
                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">Distribution:</span> {document.distributionMethod}
                </div>
              )}
              {document.remarks && (
                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">Remarks:</span> {document.remarks}
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DocumentCard;
