import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { HelpCircle, ExternalLink, BookOpen } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { ICON_SIZES } from '@/constants/ui';
import { cn } from '@/lib/utils';

interface ContextualHelpProps {
  topic: string;
  className?: string;
  variant?: 'icon' | 'button' | 'text';
  size?: 'sm' | 'md' | 'lg';
}

// Help content database
const helpContent: Record<string, {
  title: string;
  description: string;
  quickTips?: string[];
  guideSection?: string;
  externalLinks?: Array<{
    title: string;
    url: string;
  }>;
}> = {
  'hazard-analysis': {
    title: 'Hazard Analysis',
    description: 'Identify and assess potential biological, chemical, and physical hazards in your food production process.',
    quickTips: [
      'Consider all ingredients, processing steps, and environmental factors',
      'Use the risk matrix to evaluate severity and likelihood',
      'Document control measures for each identified hazard'
    ],
    guideSection: 'hazard-analysis',
    externalLinks: [
      {
        title: 'FDA HACCP Guidelines',
        url: 'https://www.fda.gov/food/hazard-analysis-critical-control-point-haccp'
      }
    ]
  },
  'ccp-determination': {
    title: 'Critical Control Points',
    description: 'Determine which process steps are critical for preventing, eliminating, or reducing hazards to acceptable levels.',
    quickTips: [
      'Use the decision tree to systematically evaluate each hazard',
      'Consider if control at this step is necessary for safety',
      'Ensure monitoring procedures can be implemented'
    ],
    guideSection: 'ccp-management'
  },
  'document-management': {
    title: 'Document Management',
    description: 'Organize and control your HACCP documents with proper version control and approval workflows.',
    quickTips: [
      'Use the standard reference format (XX.XX.01)',
      'Ensure all required metadata is completed',
      'Follow approval workflows for document changes'
    ],
    guideSection: 'document-management'
  },
  'capa-management': {
    title: 'CAPA Management',
    description: 'Manage Corrective and Preventive Actions to address non-conformities and prevent recurrence.',
    quickTips: [
      'Link CAPAs to specific claims or audit findings',
      'Conduct thorough root cause analysis',
      'Verify effectiveness of implemented actions'
    ],
    guideSection: 'capa-management'
  },
  'risk-assessment': {
    title: 'Risk Assessment',
    description: 'Evaluate the severity and likelihood of hazards using the standardized risk matrix.',
    quickTips: [
      'Severity: Consider potential health impact (1-5 scale)',
      'Likelihood: Assess probability of occurrence (1-5 scale)',
      'Risk Score = Severity × Likelihood'
    ],
    guideSection: 'risk-assessment'
  },
  'allergen-management': {
    title: 'Allergen Management',
    description: 'Control allergen risks through proper identification, segregation, and labeling procedures.',
    quickTips: [
      'Maintain updated allergen matrices',
      'Implement cleaning validation procedures',
      'Ensure accurate product labeling'
    ],
    guideSection: 'allergen-management'
  },
  'prerequisite-programs': {
    title: 'Prerequisite Programs',
    description: 'Establish foundation programs that support the HACCP system effectiveness.',
    quickTips: [
      'Cover all basic sanitation and hygiene requirements',
      'Ensure programs are documented and verified',
      'Train personnel on program requirements'
    ],
    guideSection: 'prerequisite-programs'
  }
};

export const ContextualHelp: React.FC<ContextualHelpProps> = ({
  topic,
  className,
  variant = 'icon',
  size = 'md'
}) => {
  const navigate = useNavigate();
  const content = helpContent[topic];

  if (!content) {
    return null;
  }

  const iconSize = {
    sm: ICON_SIZES.xs,
    md: ICON_SIZES.sm,
    lg: ICON_SIZES.md
  }[size];

  const renderTrigger = () => {
    switch (variant) {
      case 'button':
        return (
          <Button variant="outline" size={size} className={className}>
            <HelpCircle className={iconSize} />
            Help
          </Button>
        );
      case 'text':
        return (
          <button className={cn("text-primary hover:underline text-sm", className)}>
            Need help with {content.title.toLowerCase()}?
          </button>
        );
      default:
        return (
          <Button 
            variant="ghost" 
            size={size} 
            className={cn("hover:bg-primary/10", className)}
            aria-label={`Get help with ${content.title}`}
          >
            <HelpCircle className={iconSize} />
          </Button>
        );
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        {renderTrigger()}
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-sm flex items-center gap-2">
              <HelpCircle className={ICON_SIZES.sm} />
              {content.title}
            </h4>
            <p className="text-sm text-muted-foreground mt-1">
              {content.description}
            </p>
          </div>

          {content.quickTips && (
            <div>
              <h5 className="font-medium text-xs text-muted-foreground uppercase tracking-wide mb-2">
                Quick Tips
              </h5>
              <ul className="space-y-1">
                {content.quickTips.map((tip, index) => (
                  <li key={index} className="text-xs text-muted-foreground flex items-start gap-1">
                    <span className="text-primary mt-1">•</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex flex-col gap-2 pt-2 border-t">
            {content.guideSection && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`/user-guide#${content.guideSection}`)}
                className="justify-start h-8"
              >
                <BookOpen className={ICON_SIZES.xs} />
                View Full Guide
              </Button>
            )}

            {content.externalLinks?.map((link, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={() => window.open(link.url, '_blank')}
                className="justify-start h-8 text-xs"
              >
                <ExternalLink className={ICON_SIZES.xs} />
                {link.title}
              </Button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

// Hook for getting help content
export const useHelpContent = (topic: string) => {
  return helpContent[topic] || null;
};

// Helper component for inline help text
interface InlineHelpProps {
  children: React.ReactNode;
  topic: string;
}

export const InlineHelp: React.FC<InlineHelpProps> = ({ children, topic }) => (
  <div className="flex items-center gap-2">
    {children}
    <ContextualHelp topic={topic} size="sm" />
  </div>
);

export default ContextualHelp;
