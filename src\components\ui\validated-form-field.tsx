import React, { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { ICON_SIZES } from '@/constants/ui';

interface ValidatedFormFieldProps {
  name: string;
  label: string;
  required?: boolean;
  validation?: (value: any) => string | null;
  children: React.ReactNode;
  className?: string;
  helpText?: string;
  showValidIcon?: boolean;
}

export const ValidatedFormField: React.FC<ValidatedFormFieldProps> = ({
  name,
  label,
  required = false,
  validation,
  children,
  className,
  helpText,
  showValidIcon = true
}) => {
  const [error, setError] = useState<string | null>(null);
  const [touched, setTouched] = useState(false);
  const [value, setValue] = useState<any>('');
  const [isValid, setIsValid] = useState(false);

  const validateField = (fieldValue: any) => {
    if (required && (!fieldValue || fieldValue.toString().trim() === '')) {
      return `${label} is required`;
    }
    if (validation && fieldValue) {
      return validation(fieldValue);
    }
    return null;
  };

  const handleBlur = (e: any) => {
    setTouched(true);
    const fieldValue = e.target.value;
    setValue(fieldValue);
    const validationError = validateField(fieldValue);
    setError(validationError);
    setIsValid(!validationError && fieldValue);
  };

  const handleChange = (e: any) => {
    const fieldValue = e.target.value;
    setValue(fieldValue);
    
    // Clear error on change if field was previously touched
    if (touched) {
      const validationError = validateField(fieldValue);
      setError(validationError);
      setIsValid(!validationError && fieldValue);
    }
  };

  // Clone children to add necessary props
  const enhancedChildren = React.cloneElement(children as React.ReactElement, {
    id: name,
    name,
    'aria-required': required,
    'aria-invalid': !!error,
    'aria-describedby': [
      error ? `${name}-error` : null,
      helpText ? `${name}-help` : null
    ].filter(Boolean).join(' ') || undefined,
    onBlur: handleBlur,
    onChange: handleChange,
    className: cn(
      (children as React.ReactElement).props.className,
      error && touched && 'border-destructive focus:border-destructive',
      isValid && touched && 'border-green-500 focus:border-green-500'
    )
  });

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label 
          htmlFor={name} 
          className={cn(
            "text-sm font-medium",
            required && "after:content-['*'] after:text-destructive after:ml-1"
          )}
        >
          {label}
        </Label>
        {showValidIcon && isValid && touched && (
          <CheckCircle className={cn(ICON_SIZES.sm, "text-green-600")} />
        )}
      </div>
      
      {enhancedChildren}
      
      {helpText && !error && (
        <p id={`${name}-help`} className="text-xs text-muted-foreground">
          {helpText}
        </p>
      )}
      
      {error && touched && (
        <p id={`${name}-error`} className="text-sm text-destructive flex items-center gap-1">
          <AlertCircle className={ICON_SIZES.xs} />
          {error}
        </p>
      )}
    </div>
  );
};

// Common validation functions
export const validationRules = {
  required: (value: any) => {
    if (!value || value.toString().trim() === '') {
      return 'This field is required';
    }
    return null;
  },
  
  email: (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (value && !emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  },
  
  minLength: (min: number) => (value: string) => {
    if (value && value.length < min) {
      return `Must be at least ${min} characters long`;
    }
    return null;
  },
  
  maxLength: (max: number) => (value: string) => {
    if (value && value.length > max) {
      return `Must be no more than ${max} characters long`;
    }
    return null;
  },
  
  numeric: (value: string) => {
    if (value && isNaN(Number(value))) {
      return 'Please enter a valid number';
    }
    return null;
  },
  
  range: (min: number, max: number) => (value: string) => {
    const num = Number(value);
    if (value && (isNaN(num) || num < min || num > max)) {
      return `Value must be between ${min} and ${max}`;
    }
    return null;
  },
  
  documentReference: (value: string) => {
    // Format: XX.XX.01 (e.g., PR.01.01)
    const refRegex = /^[A-Z]{2}\.\d{2}\.\d{2}$/;
    if (value && !refRegex.test(value)) {
      return 'Format should be XX.XX.01 (e.g., PR.01.01)';
    }
    return null;
  },
  
  combine: (...validators: Array<(value: any) => string | null>) => (value: any) => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) return error;
    }
    return null;
  }
};

export default ValidatedFormField;
