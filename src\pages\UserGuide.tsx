import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  BookOpen,
  ChevronRight,
  ChevronDown,
  Printer,
  Download,
  Home,
  Menu,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';

// User Guide content sections
const userGuideContent = {
  title: "HACCP Plan Pilot - User Guide",
  sections: [
    {
      id: "getting-started",
      title: "Getting Started",
      content: `Welcome to HACCP Plan Pilot, your comprehensive Food Safety Management System. This guide will help you navigate and utilize all features of the enhanced application interface.

### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection for external resources
- JavaScript enabled

### User Roles
The application supports four user roles with different access levels:
- **Admin**: Full system access and management capabilities
- **QA Manager**: Quality assurance functions and plan management
- **Auditor**: Review and audit capabilities
- **Viewer**: Read-only access to most sections`
    },
    {
      id: "navigation-overview",
      title: "Navigation Overview",
      content: `### Enhanced TopNav Component

The TopNav is your primary navigation hub, featuring a modern, responsive design that adapts to your screen size and provides quick access to all application features.

#### Main Navigation Elements

**🏠 Brand Area (Left)**
- **HACCP Plan Pilot Logo**: Click to return to dashboard
- **System Subtitle**: "Food Safety Management System"

**⚡ Quick Access Menu (Center - Desktop Only)**
- Hover over "Quick Access" to see a dropdown with the 6 most important sections
- Each item shows an icon, title, and description
- Only displays sections you have permission to access
- Organized in a responsive grid layout

**🔧 Action Bar (Right)**
- Search button (🔍)
- Theme toggle (☀️/🌙/💻)
- Notifications (🔔)
- Help & Resources (❓)
- User Profile dropdown

#### Sidebar Navigation

**Desktop Sidebar Features:**
- **Collapsible Design**: Click the menu button (☰) to expand/collapse
- **Icon + Label**: When expanded, shows both icons and section names
- **Icon Only**: When collapsed, shows only icons with tooltips
- **Active State**: Current section is highlighted
- **Smooth Transitions**: Animated expand/collapse

**Available Sections:**
- 📊 Dashboard - Overview and analytics
- 📋 Prerequisite Programs - Foundation programs for food safety
- 👥 Food Safety Culture - Culture assessment and training
- 🛡️ Allergen Management - Allergen control and labeling
- ⚠️ CAPA Management - Corrective and preventive actions
- 📁 Document Management - Document control system
- ⚠️ Hazard Analysis - Identify and assess hazards
- ✅ CCP Management - Critical control points
- 📄 Plan Generator - Generate HACCP plans
- ⚙️ Settings - Application configuration (QA+ roles only)`
    },
    {
      id: "search-functionality",
      title: "Search Functionality",
      content: `### Global Search System

The global search feature allows you to quickly find and navigate to any section of the application.

#### How to Access Search

**Method 1: Keyboard Shortcut**
- Press \`Ctrl+K\` (Windows/Linux) or \`Cmd+K\` (Mac)
- Works from anywhere in the application
- Most efficient method for power users

**Method 2: Search Button**
- Click the search icon (🔍) in the TopNav
- Available on desktop and tablet views

#### Using Search

1. **Open Search Dialog**: Use keyboard shortcut or click search button
2. **Type Your Query**: Start typing in the search field
3. **View Results**: Matching sections appear instantly below
4. **Navigate**: Click on a result or press Enter to navigate
5. **Close**: Press Escape or click outside to close

#### Search Capabilities

**What You Can Search:**
- **Section Names**: "Dashboard", "HACCP", "Document", etc.
- **Section Descriptions**: "analytics", "food safety", "control points", etc.
- **Partial Matches**: Search works with partial words

**Search Features:**
- **Real-time Results**: Results appear as you type
- **Permission-Aware**: Only shows sections you can access
- **Intelligent Matching**: Searches both titles and descriptions
- **Instant Navigation**: Click any result to navigate immediately

#### Search Tips

**Effective Search Strategies:**
- Use keywords related to your task: "document", "hazard", "plan"
- Try abbreviations: "CCP", "CAPA", "QA"
- Use descriptive terms: "analysis", "management", "control"

**Example Searches:**
- "doc" → Document Management
- "hazard" → Hazard Analysis
- "plan" → Plan Generator
- "culture" → Food Safety Culture`
    },
    {
      id: "theme-management",
      title: "Theme Management",
      content: `### Theme Options

The application supports three theme modes to suit your preferences and working environment:

#### Available Themes

**☀️ Light Theme**
- Bright, clean interface
- Ideal for well-lit environments
- High contrast for easy reading
- Default theme for new users

**🌙 Dark Theme**
- Dark background with light text
- Reduces eye strain in low-light conditions
- Modern, professional appearance
- Battery-friendly on OLED displays

**💻 System Theme**
- Automatically matches your device's theme setting
- Switches between light and dark based on system preferences
- Updates automatically when system theme changes
- Ideal for users who change themes throughout the day

#### How to Change Themes

**Method 1: Theme Toggle Button (Desktop)**
- Click the theme icon in the TopNav (☀️/🌙/💻)
- Cycles through: Light → Dark → System → Light
- Instant visual feedback with appropriate icons

**Method 2: Mobile Menu**
- Open mobile menu (☰)
- Tap "Toggle Theme" button
- Same cycling behavior as desktop

**Method 3: Settings Page**
- Navigate to Settings (if you have access)
- Find "Appearance" section
- Select your preferred theme from buttons

#### Theme Persistence

- **Automatic Saving**: Your theme preference is saved automatically
- **Cross-Session**: Theme persists when you log out and back in
- **Cross-Device**: Theme preference syncs across devices (when logged in)
- **Instant Application**: Changes apply immediately without page refresh`
    },
    {
      id: "notifications-system",
      title: "Notifications System",
      content: `### Understanding Notifications

The notification system keeps you informed about important HACCP-related events and required actions.

#### Notification Badge

**Location**: Bell icon (🔔) in TopNav
**Badge Indicator**: Red circle with number showing unread count
**Visibility**: Badge appears when you have unread notifications

#### Types of HACCP Notifications

**🔍 HACCP Plan Review Due**
- Alerts when HACCP plans require periodic review
- Shows product name and deadline
- Critical for maintaining compliance

**🌡️ CCP Monitoring Alert**
- Notifies of critical control point deviations
- Immediate attention required
- Includes specific parameter and reading

**📄 Document Update**
- Informs about new or updated procedures
- Includes document type and brief description
- Helps maintain current knowledge

**✅ Verification Activity Due**
- Reminds about scheduled verification tasks
- Shows activity type and due date
- Ensures systematic verification

**👥 Training Reminder**
- Alerts about upcoming or overdue training
- Includes training type and personnel
- Maintains competency requirements

#### Viewing Notifications

**Access Notifications:**
1. Click the bell icon (🔔) in TopNav
2. Dropdown shows recent notifications
3. Each notification includes:
   - Title and description
   - Timestamp
   - Priority indicator

**Notification Actions:**
- **View Details**: Click notification to see full details
- **Mark as Read**: Automatically marked when viewed
- **View All**: Link to comprehensive notifications page`
    },
    {
      id: "help-resources",
      title: "Help & Resources",
      content: `### Accessing Help

The help system provides comprehensive support and access to external regulatory resources.

#### Help Menu Access

**Location**: Question mark icon (❓) in TopNav
**Availability**: Always accessible to all users
**Content**: Internal help and external resources

#### Internal Help Resources

**📖 User Guide**
- Comprehensive application documentation
- Step-by-step instructions
- Feature explanations and tips
- Keyboard shortcut: \`F1\`

**⌨️ Keyboard Shortcuts**
- Complete list of available shortcuts
- Organized by context and function
- Printable reference guide
- Keyboard shortcut: \`?\`

#### External Regulatory Resources

**🏛️ FDA HACCP Guidelines**
- Official FDA HACCP guidance documents
- Hazard Analysis and Critical Control Point principles
- Application guidelines for food processors
- Direct link to FDA website

**🏆 FSSC 22000 Standard**
- Food Safety System Certification information
- International food safety management standard
- Certification requirements and processes
- Link to official FSSC website

**🌍 Codex HACCP Guidelines**
- International food standards from Codex Alimentarius
- WHO/FAO food safety guidelines
- Global best practices and recommendations
- Link to official Codex website`
    },
    {
      id: "user-profile-settings",
      title: "User Profile & Settings",
      content: `### User Profile Management

Your user profile contains important information about your account and role within the HACCP system.

#### Viewing Profile Information

**Profile Access:**
- Click your avatar/initials in TopNav
- Dropdown shows current user information
- Displays name, email, and role badge

**Profile Information Displayed:**
- **Full Name**: Your display name in the system
- **Email Address**: Account email and login identifier
- **Role Badge**: Current permission level (Admin, QA, Auditor, Viewer)
- **Avatar**: Initials-based visual identifier

#### Role-Based Access

**Admin Role:**
- Full system access and configuration
- User management capabilities
- System settings and maintenance
- All HACCP plan functions

**QA Manager Role:**
- Quality assurance functions
- HACCP plan creation and modification
- Document management
- Audit and verification activities

**Auditor Role:**
- Review and audit capabilities
- Read access to most sections
- Verification activity management
- Compliance reporting

**Viewer Role:**
- Read-only access to plans and documents
- Dashboard and reporting access
- Limited modification capabilities
- Training and awareness materials`
    },
    {
      id: "keyboard-shortcuts",
      title: "Keyboard Shortcuts Reference",
      content: `### Global Shortcuts

| Shortcut | Action | Description |
|----------|--------|-------------|
| \`Ctrl+K\` / \`Cmd+K\` | Open Search | Global search dialog |
| \`Escape\` | Close Dialog | Close any open dialog/modal |
| \`F1\` | Help | Open user guide |
| \`?\` | Shortcuts | Show keyboard shortcuts |

### Navigation Shortcuts

| Shortcut | Action | Description |
|----------|--------|-------------|
| \`Alt+1\` | Dashboard | Navigate to dashboard |
| \`Alt+2\` | Prerequisite Programs | Navigate to prerequisite programs |
| \`Alt+3\` | Food Safety Culture | Navigate to food safety culture |
| \`Alt+4\` | Allergen Management | Navigate to allergen management |
| \`Alt+5\` | CAPA Management | Navigate to CAPA management |

### User Account Shortcuts

| Shortcut | Action | Description |
|----------|--------|-------------|
| \`⇧⌘P\` | Profile | Open user profile |
| \`⌘,\` | Settings | Open settings |
| \`⇧⌘Q\` | Logout | Logout from application |

### Search Shortcuts

| Shortcut | Action | Description |
|----------|--------|-------------|
| \`Enter\` | Execute Search | Navigate to first result |
| \`↑\` / \`↓\` | Navigate Results | Move through search results |
| \`Escape\` | Close Search | Close search dialog |

### Document Management Shortcuts

| Shortcut | Action | Description |
|----------|--------|-------------|
| \`Ctrl+N\` / \`Cmd+N\` | New Document | Create new document |
| \`Ctrl+S\` / \`Cmd+S\` | Save | Save current document |
| \`Ctrl+P\` / \`Cmd+P\` | Print | Print current view |
| \`Ctrl+E\` / \`Cmd+E\` | Edit | Edit current item |`
    }
  ]
};

interface TableOfContentsProps {
  sections: typeof userGuideContent.sections;
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const TableOfContents: React.FC<TableOfContentsProps> = ({
  sections,
  activeSection,
  onSectionClick,
  isOpen,
  onToggle
}) => {
  return (
    <Card className="sticky top-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Table of Contents
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="md:hidden"
          >
            {isOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      <CardContent className={cn("pt-0", !isOpen && "hidden md:block")}>
        <ScrollArea className="h-[400px]">
          <nav className="space-y-1">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => {
                  onSectionClick(section.id);
                  if (window.innerWidth < 768) onToggle();
                }}
                className={cn(
                  "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
                  "hover:bg-muted flex items-center gap-2",
                  activeSection === section.id
                    ? "bg-primary/10 text-primary font-medium"
                    : "text-muted-foreground"
                )}
              >
                <ChevronRight className="h-3 w-3 flex-shrink-0" />
                {section.title}
              </button>
            ))}
          </nav>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default function UserGuide() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState('getting-started');
  const [tocOpen, setTocOpen] = useState(false);
  const [filteredSections, setFilteredSections] = useState(userGuideContent.sections);

  // Filter sections based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredSections(userGuideContent.sections);
      return;
    }

    const filtered = userGuideContent.sections.filter(section =>
      section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      section.content.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredSections(filtered);
  }, [searchQuery]);

  // Handle section navigation
  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  // Handle download (placeholder)
  const handleDownload = () => {
    // In a real implementation, this would generate and download a PDF
    console.log('Download functionality would be implemented here');
  };

  // Render markdown-like content
  const renderContent = (content: string) => {
    const lines = content.split('\n');
    const elements: React.ReactNode[] = [];
    let inTable = false;
    let tableHeaders: string[] = [];
    let tableRows: string[][] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Handle table detection
      if (line.includes('|') && line.trim().startsWith('|')) {
        if (!inTable) {
          inTable = true;
          tableHeaders = line.split('|').map(h => h.trim()).filter(h => h);
          continue;
        } else if (line.includes('---')) {
          continue; // Skip separator line
        } else {
          const row = line.split('|').map(c => c.trim()).filter(c => c);
          tableRows.push(row);
          continue;
        }
      } else if (inTable) {
        // End of table, render it
        elements.push(
          <div key={`table-${i}`} className="overflow-x-auto my-4">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-muted">
                  {tableHeaders.map((header, idx) => (
                    <th key={idx} className="border border-gray-300 px-4 py-2 text-left font-semibold">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableRows.map((row, rowIdx) => (
                  <tr key={rowIdx} className={rowIdx % 2 === 0 ? 'bg-background' : 'bg-muted/50'}>
                    {row.map((cell, cellIdx) => (
                      <td key={cellIdx} className="border border-gray-300 px-4 py-2">
                        {cell.startsWith('`') && cell.endsWith('`') ? (
                          <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
                            {cell.slice(1, -1)}
                          </code>
                        ) : cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
        inTable = false;
        tableHeaders = [];
        tableRows = [];
      }

      // Regular content rendering
      if (!inTable) {
        if (line.startsWith('### ')) {
          elements.push(
            <h3 key={i} className="text-xl font-semibold mt-6 mb-3 text-foreground" role="heading" aria-level={3}>
              {line.replace('### ', '')}
            </h3>
          );
        } else if (line.startsWith('#### ')) {
          elements.push(
            <h4 key={i} className="text-lg font-medium mt-4 mb-2 text-foreground" role="heading" aria-level={4}>
              {line.replace('#### ', '')}
            </h4>
          );
        } else if (line.startsWith('**') && line.endsWith('**')) {
          elements.push(
            <p key={i} className="font-semibold mt-3 mb-2 text-foreground">
              {line.replace(/\*\*/g, '')}
            </p>
          );
        } else if (line.startsWith('- ')) {
          elements.push(
            <li key={i} className="ml-4 mb-1 text-muted-foreground list-disc">
              {line.replace('- ', '').replace(/`([^`]+)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm font-mono">$1</code>')}
            </li>
          );
        } else if (line.trim() === '') {
          elements.push(<br key={i} />);
        } else {
          // Handle inline code and formatting
          const processedLine = line
            .replace(/`([^`]+)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm font-mono">$1</code>')
            .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

          elements.push(
            <p key={i} className="mb-2 text-muted-foreground leading-relaxed"
               dangerouslySetInnerHTML={{ __html: processedLine }} />
          );
        }
      }
    }

    // Handle any remaining table
    if (inTable && tableRows.length > 0) {
      elements.push(
        <div key="final-table" className="overflow-x-auto my-4">
          <table className="min-w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-muted">
                {tableHeaders.map((header, idx) => (
                  <th key={idx} className="border border-gray-300 px-4 py-2 text-left font-semibold">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {tableRows.map((row, rowIdx) => (
                <tr key={rowIdx} className={rowIdx % 2 === 0 ? 'bg-background' : 'bg-muted/50'}>
                  {row.map((cell, cellIdx) => (
                    <td key={cellIdx} className="border border-gray-300 px-4 py-2">
                      {cell.startsWith('`') && cell.endsWith('`') ? (
                        <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
                          {cell.slice(1, -1)}
                        </code>
                      ) : cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }

    return elements;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card print-hidden">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
                <BookOpen className="h-8 w-8 text-primary" />
                {userGuideContent.title}
              </h1>
              <p className="text-muted-foreground mt-2">
                Comprehensive guide to using the HACCP Plan Pilot application
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="border-b bg-muted/30 print-hidden">
        <div className="container mx-auto px-4 py-4">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search user guide..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              aria-label="Search user guide content"
            />
          </div>
          {searchQuery && (
            <div className="mt-2">
              <Badge variant="secondary">
                {filteredSections.length} section{filteredSections.length !== 1 ? 's' : ''} found
              </Badge>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Table of Contents */}
          <div className="lg:col-span-1 print-hidden">
            <TableOfContents
              sections={filteredSections}
              activeSection={activeSection}
              onSectionClick={handleSectionClick}
              isOpen={tocOpen}
              onToggle={() => setTocOpen(!tocOpen)}
            />
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="space-y-8">
              {filteredSections.map((section, index) => (
                <Card key={section.id} id={section.id} className="scroll-mt-4 print-break-inside-avoid">
                  <CardHeader>
                    <CardTitle className="text-2xl flex items-center gap-3" role="heading" aria-level={2}>
                      <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold text-sm print-hidden">
                        {index + 1}
                      </span>
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-sm max-w-none">
                      {renderContent(section.content)}
                    </div>
                  </CardContent>
                  {index < filteredSections.length - 1 && (
                    <div className="px-6 pb-6">
                      <Separator />
                    </div>
                  )}
                </Card>
              ))}
            </div>

            {filteredSections.length === 0 && (
              <Card>
                <CardContent className="py-12 text-center">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">No results found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search terms or browse the table of contents.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
