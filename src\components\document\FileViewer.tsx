import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, Download, FileText, AlertCircle } from 'lucide-react';
import { viewFile, downloadFile, isViewableInBrowser, getFileTypeDescription } from '@/utils/fileUtils';

interface FileViewerProps {
  fileName: string;
  fileData: string;
  className?: string;
}

/**
 * FileViewer component provides a consistent interface for viewing and downloading files
 * with proper file type detection and user feedback
 */
const FileViewer: React.FC<FileViewerProps> = ({ fileName, fileData, className }) => {
  const canView = isViewableInBrowser(fileName);
  const fileType = getFileTypeDescription(fileName);

  const handleView = () => {
    viewFile(fileData, fileName);
  };

  const handleDownload = () => {
    downloadFile(fileData, fileName);
  };

  if (!fileName || !fileData) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <div className="text-center text-muted-foreground">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>No file attached</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <FileText className="h-4 w-4" />
          {fileName}
        </CardTitle>
        <p className="text-xs text-muted-foreground">{fileType}</p>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex gap-2">
          <Button
            variant="default"
            size="sm"
            onClick={handleView}
            className="flex-1"
          >
            <Eye className="h-4 w-4 mr-2" />
            {canView ? 'View' : 'Open'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex-1"
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
        {!canView && (
          <p className="text-xs text-muted-foreground mt-2">
            This file type may download instead of opening for viewing
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default FileViewer;
