/**
 * UI Constants for consistent design system
 */

// Icon sizes for consistent usage across the application
export const ICON_SIZES = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4', 
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
  xl: 'h-8 w-8',
  '2xl': 'h-10 w-10'
} as const;

// Icon size type for TypeScript
export type IconSize = keyof typeof ICON_SIZES;

// Spacing constants
export const SPACING = {
  xs: 'gap-1',
  sm: 'gap-2',
  md: 'gap-3',
  lg: 'gap-4',
  xl: 'gap-6',
  '2xl': 'gap-8'
} as const;

// Border radius constants
export const BORDER_RADIUS = {
  none: 'rounded-none',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  full: 'rounded-full'
} as const;

// Shadow constants
export const SHADOWS = {
  none: 'shadow-none',
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl'
} as const;

// Animation durations
export const ANIMATION_DURATION = {
  fast: 'duration-150',
  normal: 'duration-200',
  slow: 'duration-300'
} as const;

// Z-index layers
export const Z_INDEX = {
  dropdown: 'z-10',
  sticky: 'z-20',
  modal: 'z-30',
  popover: 'z-40',
  tooltip: 'z-50'
} as const;

// Food safety specific colors
export const FOOD_SAFETY_COLORS = {
  biological: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-500'
  },
  chemical: {
    bg: 'bg-purple-100',
    text: 'text-purple-800',
    border: 'border-purple-500'
  },
  physical: {
    bg: 'bg-orange-100',
    text: 'text-orange-800',
    border: 'border-orange-500'
  },
  allergen: {
    bg: 'bg-red-100',
    text: 'text-red-800',
    border: 'border-red-500'
  }
} as const;

// Risk level colors
export const RISK_COLORS = {
  low: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-500',
    hover: 'hover:bg-green-200'
  },
  medium: {
    bg: 'bg-yellow-100',
    text: 'text-yellow-800',
    border: 'border-yellow-500',
    hover: 'hover:bg-yellow-200'
  },
  high: {
    bg: 'bg-red-100',
    text: 'text-red-800',
    border: 'border-red-500',
    hover: 'hover:bg-red-200'
  }
} as const;

// Status colors
export const STATUS_COLORS = {
  open: {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    border: 'border-blue-500'
  },
  'in-progress': {
    bg: 'bg-yellow-100',
    text: 'text-yellow-800',
    border: 'border-yellow-500'
  },
  completed: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-500'
  },
  closed: {
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    border: 'border-gray-500'
  },
  rejected: {
    bg: 'bg-red-100',
    text: 'text-red-800',
    border: 'border-red-500'
  }
} as const;

// Priority colors
export const PRIORITY_COLORS = {
  low: {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    border: 'border-blue-500'
  },
  medium: {
    bg: 'bg-yellow-100',
    text: 'text-yellow-800',
    border: 'border-yellow-500'
  },
  high: {
    bg: 'bg-red-100',
    text: 'text-red-800',
    border: 'border-red-500'
  },
  critical: {
    bg: 'bg-red-200',
    text: 'text-red-900',
    border: 'border-red-600'
  }
} as const;
