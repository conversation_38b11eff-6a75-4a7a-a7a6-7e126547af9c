
import { useState } from 'react';
import { NavLink, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { LayoutGrid } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TopNav, navItems } from './TopNav';



export function DashboardLayout() {
  const { hasPermission } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();

  // Get current page info
  const getCurrentPageInfo = () => {
    const currentPath = location.pathname;
    const currentItem = navItems.find(item =>
      item.end ? currentPath === item.to : currentPath.startsWith(item.to)
    );
    return currentItem || { label: 'Dashboard', icon: <LayoutGrid className="h-5 w-5" /> };
  };

  const currentPageInfo = getCurrentPageInfo();

  return (
    <div className="min-h-screen flex flex-col">
      {/* Enhanced TopNav Component */}
      <TopNav
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        currentPageInfo={currentPageInfo}
      />

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside
          className={cn(
            "bg-muted border-r transition-all duration-200 ease-in-out z-10",
            sidebarOpen ? "w-64" : "w-16"
          )}
        >
          <nav className="p-2 space-y-1">
            {navItems.map((item) => (
              hasPermission(item.requiredRole as any) && (
                <NavLink
                  key={item.to}
                  to={item.to}
                  end={item.end}
                  className={({ isActive }) => cn(
                    "flex items-center px-3 py-2 rounded-md hover:bg-muted-foreground/10 transition-colors duration-150",
                    isActive ? "bg-muted-foreground/10 text-primary font-medium" : "text-muted-foreground",
                    sidebarOpen ? "justify-start" : "justify-center"
                  )}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  {sidebarOpen && <span className="ml-3">{item.label}</span>}
                </NavLink>
              )
            ))}
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 overflow-auto p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default DashboardLayout;
