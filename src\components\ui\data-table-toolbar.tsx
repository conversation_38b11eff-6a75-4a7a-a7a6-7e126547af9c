import * as React from "react"
import { Search, Filter, X, SortAsc, SortDesc } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export interface FilterOption {
  label: string
  value: string
  count?: number
}

export interface SortOption {
  label: string
  value: string
  direction?: 'asc' | 'desc'
}

export interface DataTableToolbarProps {
  searchValue?: string
  onSearchChange?: (value: string) => void
  searchPlaceholder?: string
  
  // Filter options
  filterOptions?: {
    label: string
    key: string
    options: FilterOption[]
    value?: string[]
    onValueChange?: (values: string[]) => void
  }[]
  
  // Sort options
  sortOptions?: SortOption[]
  sortValue?: string
  onSortChange?: (value: string) => void
  
  // Active filters display
  activeFilters?: { key: string; label: string; value: string }[]
  onRemoveFilter?: (key: string, value: string) => void
  onClearAllFilters?: () => void
  
  // Additional actions
  children?: React.ReactNode
  className?: string
}

export function DataTableToolbar({
  searchValue = "",
  onSearchChange,
  searchPlaceholder = "Search...",
  filterOptions = [],
  sortOptions = [],
  sortValue,
  onSortChange,
  activeFilters = [],
  onRemoveFilter,
  onClearAllFilters,
  children,
  className,
}: DataTableToolbarProps) {
  const hasActiveFilters = activeFilters.length > 0

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main toolbar */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2 flex-1">
          {/* Search input */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="pl-9"
              aria-label="Search table data"
            />
          </div>

          {/* Filter dropdowns */}
          {filterOptions.map((filter) => (
            <DropdownMenu key={filter.key}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 border-dashed"
                  aria-label={`Filter by ${filter.label}`}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {filter.label}
                  {filter.value && filter.value.length > 0 && (
                    <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                      {filter.value.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-48">
                <DropdownMenuLabel>{filter.label}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {filter.options.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    className="flex items-center justify-between"
                    onClick={() => {
                      const currentValues = filter.value || []
                      const newValues = currentValues.includes(option.value)
                        ? currentValues.filter(v => v !== option.value)
                        : [...currentValues, option.value]
                      filter.onValueChange?.(newValues)
                    }}
                  >
                    <span className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filter.value?.includes(option.value) || false}
                        onChange={() => {}} // Handled by onClick
                        className="mr-2 h-4 w-4"
                        aria-label={`Filter by ${option.label}`}
                      />
                      {option.label}
                    </span>
                    {option.count !== undefined && (
                      <Badge variant="outline" className="ml-2 h-5 px-1 text-xs">
                        {option.count}
                      </Badge>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          ))}

          {/* Sort dropdown */}
          {sortOptions.length > 0 && (
            <Select value={sortValue} onValueChange={onSortChange}>
              <SelectTrigger className="w-40 h-9">
                <div className="flex items-center">
                  {sortValue && sortOptions.find(opt => opt.value === sortValue)?.direction === 'desc' ? (
                    <SortDesc className="mr-2 h-4 w-4" />
                  ) : (
                    <SortAsc className="mr-2 h-4 w-4" />
                  )}
                  <SelectValue placeholder="Sort by..." />
                </div>
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center">
                      {option.direction === 'desc' ? (
                        <SortDesc className="mr-2 h-4 w-4" />
                      ) : (
                        <SortAsc className="mr-2 h-4 w-4" />
                      )}
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        {/* Additional actions */}
        <div className="flex items-center gap-2">
          {children}
        </div>
      </div>

      {/* Active filters */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {activeFilters.map((filter) => (
            <Badge
              key={`${filter.key}-${filter.value}`}
              variant="secondary"
              className="gap-1"
            >
              {filter.label}: {filter.value}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-muted-foreground hover:text-foreground"
                onClick={() => onRemoveFilter?.(filter.key, filter.value)}
                aria-label={`Remove filter ${filter.label}: ${filter.value}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAllFilters}
            className="h-6 px-2 text-xs"
            aria-label="Clear all filters"
          >
            Clear all
          </Button>
        </div>
      )}
    </div>
  )
}
