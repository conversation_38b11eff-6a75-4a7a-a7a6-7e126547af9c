import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { toast } from '@/hooks/use-toast';
import {
  LayoutGrid,
  ClipboardCheck,
  AlertTriangle,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronRight,
  ClipboardList,
  Users,
  ShieldAlert,
  FileWarning,
  FolderOpen,
  ShieldCheck,
  User,
  ChevronDown,
  Bell,
  HelpCircle,
  Search,
  Sun,
  Moon,
  Monitor,
  ExternalLink,
  BookOpen,
  Zap,
  Command
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuShortcut
} from '@/components/ui/dropdown-menu';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

// Navigation items configuration
export const navItems = [
  {
    to: '/dashboard',
    icon: <LayoutGrid className="h-5 w-5" />,
    label: 'Dashboard',
    requiredRole: 'viewer' as const,
    end: true,
    description: 'Overview and analytics'
  },
  {
    to: '/prerequisite-programs',
    icon: <ClipboardList className="h-5 w-5" />,
    label: 'Prerequisite Programs',
    requiredRole: 'viewer' as const,
    description: 'Foundation programs for food safety'
  },
  {
    to: '/food-safety-culture',
    icon: <Users className="h-5 w-5" />,
    label: 'Food Safety Culture',
    requiredRole: 'viewer' as const,
    description: 'Culture assessment and training'
  },
  {
    to: '/allergen-management',
    icon: <ShieldAlert className="h-5 w-5" />,
    label: 'Allergen Management',
    requiredRole: 'viewer' as const,
    description: 'Allergen control and labeling'
  },
  {
    to: '/capa',
    icon: <FileWarning className="h-5 w-5" />,
    label: 'CAPA Management',
    requiredRole: 'viewer' as const,
    description: 'Corrective and preventive actions'
  },
  {
    to: '/document-management',
    icon: <FolderOpen className="h-5 w-5" />,
    label: 'Document Management',
    requiredRole: 'viewer' as const,
    description: 'Document control system'
  },
  {
    to: '/hazard-analysis',
    icon: <AlertTriangle className="h-5 w-5" />,
    label: 'Hazard Analysis',
    requiredRole: 'viewer' as const,
    description: 'Identify and assess hazards'
  },
  {
    to: '/ccp-management',
    icon: <ClipboardCheck className="h-5 w-5" />,
    label: 'CCP Management',
    requiredRole: 'viewer' as const,
    description: 'Critical control points'
  },
  {
    to: '/plan-generator',
    icon: <FileText className="h-5 w-5" />,
    label: 'Plan Generator',
    requiredRole: 'viewer' as const,
    description: 'Generate HACCP plans'
  },
  {
    to: '/settings',
    icon: <Settings className="h-5 w-5" />,
    label: 'Settings',
    requiredRole: 'qa' as const,
    description: 'Application configuration'
  },
  {
    to: '/user-guide',
    icon: <BookOpen className="h-5 w-5" />,
    label: 'User Guide',
    requiredRole: 'viewer' as const,
    description: 'Comprehensive application documentation'
  }
];

// External resources
const externalResources = [
  {
    title: 'FDA HACCP Guidelines',
    url: 'https://www.fda.gov/food/hazard-analysis-critical-control-point-haccp/haccp-principles-application-guidelines',
    description: 'Official FDA HACCP guidance'
  },
  {
    title: 'FSSC 22000 Standard',
    url: 'https://www.fssc22000.com/',
    description: 'Food Safety System Certification'
  },
  {
    title: 'Codex HACCP Guidelines',
    url: 'https://www.fao.org/fao-who-codexalimentarius/codex-texts/guidelines/en/',
    description: 'International food standards'
  }
];

interface TopNavProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  currentPageInfo: {
    label: string;
    icon: React.ReactNode;
  };
}

export const TopNav: React.FC<TopNavProps> = ({
  sidebarOpen,
  setSidebarOpen,
  currentPageInfo
}) => {
  const { currentUser, logout, hasPermission } = useAuth();
  const { theme, setTheme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [notificationCount, setNotificationCount] = useState(3);

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out"
      });
      navigate('/login');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout",
        variant: "destructive"
      });
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    if (!query.trim()) return;
    
    // Simple search implementation - could be enhanced with fuzzy search
    const searchResults = navItems.filter(item =>
      hasPermission(item.requiredRole) &&
      (item.label.toLowerCase().includes(query.toLowerCase()) ||
       item.description.toLowerCase().includes(query.toLowerCase()))
    );

    if (searchResults.length > 0) {
      navigate(searchResults[0].to);
      setSearchOpen(false);
      setSearchQuery('');
      toast({
        title: "Navigation",
        description: `Navigated to ${searchResults[0].label}`
      });
    } else {
      toast({
        title: "No results",
        description: "No matching pages found",
        variant: "destructive"
      });
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K for search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setSearchOpen(true);
      }
      // F1 for user guide
      if (e.key === 'F1') {
        e.preventDefault();
        navigate('/user-guide');
      }
      // Escape to close search
      if (e.key === 'Escape' && searchOpen) {
        setSearchOpen(false);
        setSearchQuery('');
      }
      // Enter to search
      if (e.key === 'Enter' && searchOpen && searchQuery) {
        handleSearch(searchQuery);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [searchOpen, searchQuery, navigate]);

  // Theme toggle
  const toggleTheme = () => {
    const themes = ['light', 'dark', 'system'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
    toast({
      title: "Theme changed",
      description: `Switched to ${nextTheme} theme`
    });
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light': return <Sun className="h-4 w-4" />;
      case 'dark': return <Moon className="h-4 w-4" />;
      case 'system': return <Monitor className="h-4 w-4" />;
      default: return <Sun className="h-4 w-4" />;
    }
  };

  return (
    <>
      {/* Main Header */}
      <header className="bg-gradient-to-r from-background via-background to-primary/5 border-b shadow-sm z-10 backdrop-blur-sm">
        <div className="mx-auto px-4 lg:px-6 flex h-16 items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center gap-4">
            {/* Sidebar Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
              className="hidden md:flex hover:bg-primary/10 transition-colors"
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>

            {/* Brand */}
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-lg">
                <ShieldCheck className="h-5 w-5 text-primary" />
              </div>
              <div className="flex flex-col">
                <h1 className="text-xl font-bold text-foreground leading-none">
                  <span className="hidden sm:inline">HACCP Plan Pilot</span>
                  <span className="sm:hidden">HACCP</span>
                </h1>
                <p className="text-xs text-muted-foreground font-medium hidden sm:block">
                  Food Safety Management System
                </p>
              </div>
            </div>
          </div>

          {/* Center Section - Quick Navigation (hidden on mobile) */}
          <div className="hidden lg:flex items-center">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="h-9 px-3 text-sm font-medium">
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Access
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid gap-3 p-6 w-[400px] lg:w-[500px] lg:grid-cols-2">
                      {navItems.slice(0, 6).map((item) => (
                        hasPermission(item.requiredRole) && (
                          <NavigationMenuLink
                            key={item.to}
                            href={item.to}
                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div className="flex items-center gap-2 text-sm font-medium leading-none">
                              {item.icon}
                              {item.label}
                            </div>
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {item.description}
                            </p>
                          </NavigationMenuLink>
                        )
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right Section */}
          <div className="flex items-center gap-2">
            {currentUser && (
              <>
                {/* Search Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchOpen(true)}
                  className="hidden md:flex hover:bg-primary/10 relative"
                  aria-label="Search"
                >
                  <Search className="h-5 w-5" />
                </Button>

                {/* Theme Toggle */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleTheme}
                  className="hidden lg:flex hover:bg-primary/10"
                  aria-label={`Switch to ${theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light'} theme`}
                >
                  {getThemeIcon()}
                </Button>

                {/* Notifications */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="hidden lg:flex hover:bg-primary/10 relative"
                      aria-label="Notifications"
                    >
                      <Bell className="h-5 w-5" />
                      {notificationCount > 0 && (
                        <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-[10px] font-bold text-destructive-foreground flex items-center justify-center">
                          {notificationCount}
                        </span>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80">
                    <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="p-2 space-y-2">
                      <div className="p-2 rounded-md bg-muted/50">
                        <p className="text-sm font-medium">HACCP Plan Review Due</p>
                        <p className="text-xs text-muted-foreground">Product XYZ plan requires review by tomorrow</p>
                      </div>
                      <div className="p-2 rounded-md bg-muted/50">
                        <p className="text-sm font-medium">CCP Monitoring Alert</p>
                        <p className="text-xs text-muted-foreground">Temperature reading outside critical limits</p>
                      </div>
                      <div className="p-2 rounded-md bg-muted/50">
                        <p className="text-sm font-medium">Document Update</p>
                        <p className="text-xs text-muted-foreground">New allergen management procedure available</p>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="cursor-pointer">
                      <span>View all notifications</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Help Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="hidden lg:flex hover:bg-primary/10"
                      aria-label="Help & Resources"
                    >
                      <HelpCircle className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-64">
                    <DropdownMenuLabel>Help & Resources</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={() => navigate('/user-guide')}
                      >
                        <BookOpen className="mr-2 h-4 w-4" />
                        <span>User Guide</span>
                        <DropdownMenuShortcut>F1</DropdownMenuShortcut>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer">
                        <Command className="mr-2 h-4 w-4" />
                        <span>Keyboard Shortcuts</span>
                        <DropdownMenuShortcut>?</DropdownMenuShortcut>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>External Resources</DropdownMenuLabel>
                    {externalResources.map((resource, index) => (
                      <DropdownMenuItem
                        key={index}
                        className="cursor-pointer"
                        onClick={() => window.open(resource.url, '_blank')}
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        <div className="flex flex-col">
                          <span className="text-sm">{resource.title}</span>
                          <span className="text-xs text-muted-foreground">{resource.description}</span>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* User Profile Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="hidden md:flex items-center gap-3 px-3 py-2 h-auto hover:bg-primary/10 transition-colors"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-primary/10 text-primary font-semibold text-sm">
                          {currentUser.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="text-left hidden lg:block">
                        <p className="font-medium text-sm leading-none">{currentUser.name}</p>
                        <p className="text-xs text-muted-foreground mt-1 capitalize">{currentUser.role}</p>
                      </div>
                      <ChevronDown className="h-4 w-4 text-muted-foreground hidden lg:block" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{currentUser.name}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {currentUser.email}
                        </p>
                        <Badge variant="secondary" className="text-xs capitalize w-fit mt-1">
                          {currentUser.role}
                        </Badge>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuItem className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                        <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={() => navigate('/settings')}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Settings</span>
                        <DropdownMenuShortcut>⌘,</DropdownMenuShortcut>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer text-destructive focus:text-destructive"
                      onClick={handleLogout}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Logout</span>
                      <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Mobile Menu Button */}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="ghost" size="icon" className="md:hidden hover:bg-primary/10">
                      <Menu className="h-5 w-5" />
                      <span className="sr-only">Toggle mobile menu</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Navigation</DialogTitle>
                      <DialogDescription>
                        Access all application features
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-6 py-4">
                      {/* User Info Section */}
                      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                            {currentUser.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">{currentUser.name}</p>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs capitalize">
                              {currentUser.role}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Navigation Items */}
                      <nav className="space-y-1">
                        {navItems.map((item) => (
                          hasPermission(item.requiredRole) && (
                            <button
                              key={item.to}
                              onClick={() => {
                                navigate(item.to);
                                // Close dialog
                                document.querySelector('[data-state="open"]')?.click();
                              }}
                              className="flex items-center w-full px-3 py-3 rounded-lg hover:bg-primary/10 transition-colors duration-150 text-left"
                            >
                              <span className="flex-shrink-0">{item.icon}</span>
                              <div className="ml-3">
                                <span className="font-medium block">{item.label}</span>
                                <span className="text-xs text-muted-foreground">{item.description}</span>
                              </div>
                            </button>
                          )
                        ))}
                      </nav>

                      {/* Mobile Actions */}
                      <div className="space-y-2 pt-4 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleTheme}
                          className="w-full justify-start"
                        >
                          {getThemeIcon()}
                          <span className="ml-2">Toggle Theme</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleLogout}
                          className="w-full justify-start text-destructive hover:text-destructive"
                        >
                          <LogOut className="h-4 w-4" />
                          <span className="ml-2">Logout</span>
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Search Dialog */}
      <Dialog open={searchOpen} onOpenChange={setSearchOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Search</DialogTitle>
            <DialogDescription>
              Search for pages and features. Press Ctrl+K to open anytime.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search pages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                autoFocus
              />
            </div>
            {searchQuery && (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {navItems
                  .filter(item =>
                    hasPermission(item.requiredRole) &&
                    (item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     item.description.toLowerCase().includes(searchQuery.toLowerCase()))
                  )
                  .map((item) => (
                    <button
                      key={item.to}
                      onClick={() => {
                        navigate(item.to);
                        setSearchOpen(false);
                        setSearchQuery('');
                      }}
                      className="flex items-center w-full p-3 rounded-lg hover:bg-muted transition-colors text-left"
                    >
                      <span className="flex-shrink-0">{item.icon}</span>
                      <div className="ml-3">
                        <span className="font-medium block">{item.label}</span>
                        <span className="text-xs text-muted-foreground">{item.description}</span>
                      </div>
                    </button>
                  ))}
                {navItems.filter(item =>
                  hasPermission(item.requiredRole) &&
                  (item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
                   item.description.toLowerCase().includes(searchQuery.toLowerCase()))
                ).length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No results found for "{searchQuery}"
                  </p>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Breadcrumb Section */}
      <div className="bg-muted/30 border-b px-4 lg:px-6 py-3">
        <div className="flex items-center gap-2 text-sm">
          <div className="flex items-center gap-2 text-muted-foreground">
            <span>HACCP Plan Pilot</span>
            <ChevronRight className="h-4 w-4" />
          </div>
          <div className="flex items-center gap-2 font-medium">
            {currentPageInfo.icon}
            <span>{currentPageInfo.label}</span>
          </div>
        </div>
      </div>
    </>
  );
};
