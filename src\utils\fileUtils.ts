/**
 * Utility functions for file handling and viewing
 */

import { toast } from 'sonner';

/**
 * Get the file extension from a filename
 */
export const getFileExtension = (fileName: string): string => {
  return fileName.split('.').pop()?.toLowerCase() || '';
};

/**
 * Check if a file type can be viewed in the browser
 */
export const isViewableInBrowser = (fileName: string): boolean => {
  const extension = getFileExtension(fileName);
  const viewableExtensions = [
    'pdf', 'txt', 'md', 'json', 'xml', 'csv',
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp',
    'html', 'htm', 'css', 'js', 'ts'
  ];
  return viewableExtensions.includes(extension);
};

/**
 * Get the MIME type for a file based on its extension
 */
export const getMimeType = (fileName: string): string => {
  const extension = getFileExtension(fileName);
  const mimeTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'txt': 'text/plain',
    'md': 'text/markdown',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'svg': 'image/svg+xml',
    'webp': 'image/webp',
    'html': 'text/html',
    'htm': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'ts': 'application/typescript',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  };
  return mimeTypes[extension] || 'application/octet-stream';
};

/**
 * Open a file for viewing in a new tab with better error handling
 */
export const viewFile = (fileData: string, fileName: string): void => {
  if (!fileData) {
    toast.error('No file data available');
    return;
  }

  if (!fileName) {
    toast.error('No file name provided');
    return;
  }

  try {
    // Check if the file can be viewed directly in the browser
    if (isViewableInBrowser(fileName)) {
      const newWindow = window.open(fileData, '_blank');
      if (newWindow) {
        toast.success(`Opening ${fileName} for viewing`);
      } else {
        toast.error('Popup blocked. Please allow popups for this site.');
      }
    } else {
      // For non-viewable files, still try to open them
      // The browser will handle them appropriately (download or show error)
      const newWindow = window.open(fileData, '_blank');
      if (newWindow) {
        toast.info(`Opening ${fileName} - your browser will handle this file type`);
      } else {
        toast.error('Popup blocked. Please allow popups for this site.');
        // Fallback: try to download the file
        downloadFile(fileData, fileName);
      }
    }
  } catch (error) {
    console.error('Error opening file:', error);
    toast.error('Failed to open file for viewing');
    // Fallback: try to download the file
    downloadFile(fileData, fileName);
  }
};

/**
 * Download a file with proper error handling
 */
export const downloadFile = (fileData: string, fileName: string): void => {
  if (!fileData) {
    toast.error('No file data available for download');
    return;
  }

  try {
    const link = document.createElement('a');
    link.href = fileData;
    link.download = fileName || 'document';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success(`Downloading ${fileName}`);
  } catch (error) {
    console.error('Error downloading file:', error);
    toast.error('Failed to download file');
  }
};

/**
 * Get a user-friendly file type description
 */
export const getFileTypeDescription = (fileName: string): string => {
  const extension = getFileExtension(fileName);
  const descriptions: Record<string, string> = {
    'pdf': 'PDF Document',
    'doc': 'Word Document',
    'docx': 'Word Document',
    'xls': 'Excel Spreadsheet',
    'xlsx': 'Excel Spreadsheet',
    'ppt': 'PowerPoint Presentation',
    'pptx': 'PowerPoint Presentation',
    'txt': 'Text File',
    'md': 'Markdown File',
    'json': 'JSON File',
    'xml': 'XML File',
    'csv': 'CSV File',
    'jpg': 'JPEG Image',
    'jpeg': 'JPEG Image',
    'png': 'PNG Image',
    'gif': 'GIF Image',
    'bmp': 'Bitmap Image',
    'svg': 'SVG Image',
    'webp': 'WebP Image',
    'html': 'HTML File',
    'htm': 'HTML File',
    'css': 'CSS File',
    'js': 'JavaScript File',
    'ts': 'TypeScript File'
  };
  return descriptions[extension] || `${extension.toUpperCase()} File`;
};
