
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { LoginForm } from '@/components/auth/login-form';
import { useAuth } from '@/contexts/AuthContext';
import { ShieldCheck } from 'lucide-react';

export function Login() {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (currentUser) {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);
  
  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-primary/5 via-background to-muted/30 p-4">
      <div className="w-full max-w-md mb-8">
        <div className="text-center mb-8">
          {/* Logo and Icon */}
          <div className="flex justify-center mb-6">
            <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl shadow-lg">
              <ShieldCheck className="h-8 w-8 text-primary" />
            </div>
          </div>

          {/* Title and Subtitle */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground tracking-tight">HACCP Plan Pilot</h1>
            <p className="text-muted-foreground font-medium">
              Food Safety Management System
            </p>
            <p className="text-sm text-muted-foreground">
              Sign in to access your HACCP compliance dashboard
            </p>
          </div>
        </div>
        <LoginForm />
      </div>
      <div className="text-center text-sm text-muted-foreground mt-6 space-y-1">
        <p>Comprehensive HACCP compliance and quality assurance platform</p>
        <p>© {new Date().getFullYear()} HACCP Plan Pilot</p>
      </div>
    </div>
  );
}

export default Login;
