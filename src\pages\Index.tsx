
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { ShieldCheck, Loader2 } from 'lucide-react';

const Index = () => {
  const navigate = useNavigate();
  const { currentUser, loading } = useAuth();
  
  useEffect(() => {
    if (!loading) {
      if (currentUser) {
        navigate('/dashboard');
      } else {
        navigate('/login');
      }
    }
  }, [currentUser, loading, navigate]);
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-muted/30">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        {/* Logo and Icon */}
        <div className="flex justify-center">
          <div className="flex items-center justify-center w-20 h-20 bg-primary/10 rounded-2xl shadow-lg">
            <ShieldCheck className="h-10 w-10 text-primary" />
          </div>
        </div>

        {/* Title and Subtitle */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-foreground tracking-tight">
            HACCP Plan Pilot
          </h1>
          <p className="text-lg text-muted-foreground font-medium">
            Food Safety Management System
          </p>
          <p className="text-sm text-muted-foreground">
            Comprehensive HACCP compliance and quality assurance platform
          </p>
        </div>

        {/* Loading Indicator */}
        <div className="flex items-center justify-center gap-2 text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm font-medium">Initializing system...</span>
        </div>
      </div>
    </div>
  );
};

export default Index;
