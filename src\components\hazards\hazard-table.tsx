
import React, { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTableToolbar, FilterOption, SortOption } from '@/components/ui/data-table-toolbar';
import { Hazard } from '@/models/types';
import { calculateRisk, getRiskColorClass } from '@/utils/riskCalculation';
import { Edit, Trash2, CheckCircle, AlertCircle, Plus } from 'lucide-react';

interface HazardTableProps {
  hazards: Hazard[];
  onEdit?: (hazard: Hazard) => void;
  onDelete?: (hazardId: string) => void;
  onViewCCP?: (hazard: Hazard) => void;
  onAdd?: () => void;
  readonly?: boolean;
  title?: string;
  description?: string;
  showToolbar?: boolean;
}

export function HazardTable({
  hazards,
  onEdit,
  onDelete,
  onViewCCP,
  onAdd,
  readonly = false,
  title = "Hazards",
  description = "Identified hazards and their risk assessments",
  showToolbar = true
}: HazardTableProps) {
  // State for search and filtering
  const [searchValue, setSearchValue] = useState("")
  const [typeFilter, setTypeFilter] = useState<string[]>([])
  const [riskFilter, setRiskFilter] = useState<string[]>([])
  const [ccpFilter, setCcpFilter] = useState<string[]>([])
  const [sortValue, setSortValue] = useState<string>("")

  // Filter and sort options
  const typeOptions: FilterOption[] = [
    { label: "Biological", value: "Biological", count: hazards.filter(h => h.type === "Biological").length },
    { label: "Chemical", value: "Chemical", count: hazards.filter(h => h.type === "Chemical").length },
    { label: "Physical", value: "Physical", count: hazards.filter(h => h.type === "Physical").length },
  ]

  const riskOptions: FilterOption[] = [
    { label: "Low", value: "low", count: hazards.filter(h => calculateRisk(h.severity, h.likelihood).level === "low").length },
    { label: "Medium", value: "medium", count: hazards.filter(h => calculateRisk(h.severity, h.likelihood).level === "medium").length },
    { label: "High", value: "high", count: hazards.filter(h => calculateRisk(h.severity, h.likelihood).level === "high").length },
  ]

  const ccpOptions: FilterOption[] = [
    { label: "CCP", value: "true", count: hazards.filter(h => h.isCCP).length },
    { label: "Not CCP", value: "false", count: hazards.filter(h => !h.isCCP).length },
  ]

  const sortOptions: SortOption[] = [
    { label: "Risk Score (High to Low)", value: "risk-desc", direction: "desc" },
    { label: "Risk Score (Low to High)", value: "risk-asc", direction: "asc" },
    { label: "Type (A to Z)", value: "type-asc", direction: "asc" },
    { label: "Type (Z to A)", value: "type-desc", direction: "desc" },
  ]

  // Filtered and sorted hazards
  const filteredHazards = useMemo(() => {
    let filtered = hazards.filter(hazard => {
      // Search filter
      const matchesSearch = searchValue === "" ||
        hazard.description.toLowerCase().includes(searchValue.toLowerCase()) ||
        hazard.type.toLowerCase().includes(searchValue.toLowerCase())

      // Type filter
      const matchesType = typeFilter.length === 0 || typeFilter.includes(hazard.type)

      // Risk filter
      const riskLevel = calculateRisk(hazard.severity, hazard.likelihood).level
      const matchesRisk = riskFilter.length === 0 || riskFilter.includes(riskLevel)

      // CCP filter
      const matchesCcp = ccpFilter.length === 0 ||
        (ccpFilter.includes("true") && hazard.isCCP) ||
        (ccpFilter.includes("false") && !hazard.isCCP)

      return matchesSearch && matchesType && matchesRisk && matchesCcp
    })

    // Apply sorting
    if (sortValue) {
      filtered = [...filtered].sort((a, b) => {
        switch (sortValue) {
          case "risk-desc":
            return calculateRisk(b.severity, b.likelihood).score - calculateRisk(a.severity, a.likelihood).score
          case "risk-asc":
            return calculateRisk(a.severity, a.likelihood).score - calculateRisk(b.severity, b.likelihood).score
          case "type-asc":
            return a.type.localeCompare(b.type)
          case "type-desc":
            return b.type.localeCompare(a.type)
          default:
            return 0
        }
      })
    }

    return filtered
  }, [hazards, searchValue, typeFilter, riskFilter, ccpFilter, sortValue])

  // Active filters for display
  const activeFilters = [
    ...typeFilter.map(value => ({ key: "type", label: "Type", value })),
    ...riskFilter.map(value => ({ key: "risk", label: "Risk", value })),
    ...ccpFilter.map(value => ({ key: "ccp", label: "CCP", value: value === "true" ? "CCP" : "Not CCP" })),
  ]

  const handleRemoveFilter = (key: string, value: string) => {
    switch (key) {
      case "type":
        setTypeFilter(prev => prev.filter(v => v !== value))
        break
      case "risk":
        setRiskFilter(prev => prev.filter(v => v !== value))
        break
      case "ccp":
        const ccpValue = value === "CCP" ? "true" : "false"
        setCcpFilter(prev => prev.filter(v => v !== ccpValue))
        break
    }
  }

  const handleClearAllFilters = () => {
    setSearchValue("")
    setTypeFilter([])
    setRiskFilter([])
    setCcpFilter([])
    setSortValue("")
  }

  if (!hazards.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {title}
            {onAdd && !readonly && (
              <Button onClick={onAdd} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Hazard
              </Button>
            )}
          </CardTitle>
          {description && <p className="text-caption">{description}</p>}
        </CardHeader>
        <CardContent>
          <div className="p-6 text-center bg-muted/30 rounded-lg border border-dashed">
            <p className="text-muted-foreground">No hazards have been identified yet.</p>
            {onAdd && !readonly && (
              <Button onClick={onAdd} className="mt-4" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add First Hazard
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
          {onAdd && !readonly && (
            <Button onClick={onAdd} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Hazard
            </Button>
          )}
        </CardTitle>
        {description && <p className="text-caption">{description}</p>}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filter Toolbar */}
        {showToolbar && (
          <DataTableToolbar
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            searchPlaceholder="Search hazards by type or description..."
            filterOptions={[
              {
                label: "Type",
                key: "type",
                options: typeOptions,
                value: typeFilter,
                onValueChange: setTypeFilter,
              },
              {
                label: "Risk Level",
                key: "risk",
                options: riskOptions,
                value: riskFilter,
                onValueChange: setRiskFilter,
              },
              {
                label: "CCP Status",
                key: "ccp",
                options: ccpOptions,
                value: ccpFilter,
                onValueChange: setCcpFilter,
              },
            ]}
            sortOptions={sortOptions}
            sortValue={sortValue}
            onSortChange={setSortValue}
            activeFilters={activeFilters}
            onRemoveFilter={handleRemoveFilter}
            onClearAllFilters={handleClearAllFilters}
          />
        )}

        {/* Results summary */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            Showing {filteredHazards.length} of {hazards.length} hazards
          </span>
          {filteredHazards.length !== hazards.length && (
            <span className="text-primary">
              {hazards.length - filteredHazards.length} filtered out
            </span>
          )}
        </div>

        {/* Table */}
        <div className="overflow-x-auto rounded-md border">
          <Table role="table" aria-label="HACCP hazards list">
            <TableHeader>
              <TableRow role="row">
                <TableHead role="columnheader" aria-sort="none">Type</TableHead>
                <TableHead role="columnheader" aria-sort="none" className="w-full">Description</TableHead>
                <TableHead role="columnheader" aria-sort="none">Severity</TableHead>
                <TableHead role="columnheader" aria-sort="none">Likelihood</TableHead>
                <TableHead role="columnheader" aria-sort="none">Risk Level</TableHead>
                <TableHead role="columnheader" aria-sort="none">CCP</TableHead>
                {!readonly && <TableHead role="columnheader" className="text-right">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredHazards.map((hazard) => {
                const { score, level } = calculateRisk(hazard.severity, hazard.likelihood);
                const riskColorClass = getRiskColorClass(level);

                return (
                  <TableRow key={hazard.id} role="row">
                    <TableCell role="cell" className="font-medium">
                      <span
                        className={`inline-block px-2 py-1 rounded-full text-xs ${
                          hazard.type === 'Biological' ? 'bg-green-100 text-green-800' :
                          hazard.type === 'Chemical' ? 'bg-purple-100 text-purple-800' :
                          'bg-orange-100 text-orange-800'
                        }`}
                        aria-label={`Hazard type: ${hazard.type}`}
                      >
                        {hazard.type}
                      </span>
                    </TableCell>
                    <TableCell role="cell">{hazard.description}</TableCell>
                    <TableCell role="cell" className="text-center" aria-label={`Severity level ${hazard.severity} out of 5`}>
                      {hazard.severity}
                    </TableCell>
                    <TableCell role="cell" className="text-center" aria-label={`Likelihood level ${hazard.likelihood} out of 5`}>
                      {hazard.likelihood}
                    </TableCell>
                    <TableCell role="cell">
                      <span
                        className={`inline-block px-2 py-1 rounded text-xs ${riskColorClass}`}
                        aria-label={`Risk score ${score}, level ${level}`}
                      >
                        {score} ({level})
                      </span>
                    </TableCell>
                    <TableCell role="cell">
                      {hazard.isCCP ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-yellow-600 hover:text-yellow-700"
                          aria-label={`View CCP details for ${hazard.description}`}
                          onClick={() => onViewCCP && onViewCCP(hazard)}
                        >
                          <CheckCircle className="h-5 w-5 mr-1" />
                          CCP
                        </Button>
                      ) : (
                        <span className="text-gray-500 flex items-center">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          No
                        </span>
                      )}
                    </TableCell>
                    {!readonly && (
                      <TableCell role="cell" className="text-right">
                        <div className="flex justify-end gap-2">
                          {onEdit && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onEdit(hazard)}
                              aria-label={`Edit hazard: ${hazard.description}`}
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                          )}
                          {onDelete && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-destructive"
                              onClick={() => onDelete(hazard.id)}
                              aria-label={`Delete hazard: ${hazard.description}`}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

export default HazardTable;
